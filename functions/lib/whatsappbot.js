"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatBots = void 0;
exports.sendMessengerSenderAction = sendMessengerSenderAction;
const https_1 = require("firebase-functions/v2/https");
const logger = __importStar(require("firebase-functions/logger"));
const admin = __importStar(require("firebase-admin"));
const express_1 = __importDefault(require("express"));
// import { twiml } from "twilio";
const utils_1 = require("./utils");
const axios_1 = __importDefault(require("axios"));
const multipart = __importStar(require("parse-multipart-data"));
const mail_1 = __importDefault(require("@sendgrid/mail"));
const chatbots_1 = require("./chatbots");
const calls_1 = require("./calls");
const forms_1 = require("./forms");
const vapi_1 = require("./chatbots/vapi");
const appointments_1 = require("./appointments");
async function handelLeadDocumentVoice(utterances, req) {
    const { From, CallerName } = req;
    const leadsCollection = admin.firestore().collection("leads");
    const currentTime = Math.floor(Date.now() / 1000);
    // Search for an existing lead by WaId (WhatsApp ID)
    const leadsSnapshot = await leadsCollection
        .where("number", "==", From)
        .where("lead_source", "==", "voice")
        .get();
    let leadDocRef, leadData;
    if (!leadsSnapshot.empty) {
        // Lead exists, get the reference and data
        const leadDoc = leadsSnapshot.docs[0];
        leadDocRef = leadDoc.ref;
        leadData = leadDoc.data();
    }
    else {
        // Create a new lead document with an autogenerated ID
        leadDocRef = leadsCollection.doc(); // Auto-generate document ID
        leadData = {
            lead_details: req,
            number: From,
            name: CallerName,
            lead_source: "voice", // Track the source of the lead
            lastMessageTime: currentTime,
            conversations: [],
        };
    }
    utterances.forEach((utterance) => {
        const speaker = utterance.speaker;
        const message = utterance.text;
        leadData.lead_details.conversations.push({
            role: "human",
            speaker: speaker,
            message: message,
            lastMessageTime: currentTime,
        });
    });
    leadDocRef.set(leadData, { merge: true });
    return { leadDocRef: leadDocRef, leadData: leadData };
}
// Function to send a sender action (e.g., typing_on)
async function sendMessengerSenderAction(recipientId, action) {
    try {
        const data = JSON.stringify({
            recipient: {
                id: recipientId,
            },
            sender_action: action,
        });
        const config = {
            method: "post",
            url: `https://graph.facebook.com/me/messages?access_token=${utils_1.facebookPageAccessToken.value()}`,
            headers: {
                "Content-Type": "application/json",
            },
            data: data,
        };
        const response = await (0, axios_1.default)(config);
        logger.info("Sender action response:", response.data);
    }
    catch (error) {
        logger.error("Error sending sender action:", error);
    }
}
const app = (0, express_1.default)();
app.use(express_1.default.json());
app.post("/whatsapp-webhook", async (req, res) => {
    const { WaId } = req.body;
    const twilio = (0, utils_1.invokeTwilio)();
    // const { MessagingResponse } = twilio.twiml;
    // const twimlMsg = new MessagingResponse();
    const fromWhatsapp = `whatsapp:${utils_1.twilioWhatsappNumber.value()}`;
    const toWhatsapp = `whatsapp:${WaId}`;
    try {
        const messageBody = await (0, chatbots_1.handleUserQuery)("whatsapp", req.body);
        const maxMessageLength = 1600;
        if (messageBody.length > maxMessageLength) {
            const chunks = messageBody.match(new RegExp(`.{1,${maxMessageLength}}`, "g"));
            if (chunks) {
                for (const chunk of chunks) {
                    await twilio.messages.create({
                        from: fromWhatsapp,
                        body: chunk,
                        to: toWhatsapp,
                    });
                }
            }
        }
        else {
            await twilio.messages.create({
                from: fromWhatsapp,
                body: messageBody,
                to: toWhatsapp,
            });
        }
        res.writeHead(200, { "Content-Type": "text/xml" });
        res.end("Sent message successfully.");
    }
    catch (error) {
        logger.error("Error in Twilio webhook:", error);
        await twilio.messages.create({
            from: fromWhatsapp,
            body: "Sorry, there was an error processing your request.",
            to: toWhatsapp,
        });
        res.writeHead(200, { "Content-Type": "text/xml" });
        res.end("Sorry, there was an error processing your request.");
    }
});
// Incoming Call Webhook
app.post("/call-recording-webhook", async (req, res) => {
    const { RecordingUrl } = req.body;
    logger.log(`Received Recording URL: ${RecordingUrl}`);
    if (!RecordingUrl) {
        logger.error("No Recording URL provided");
        res.status(400).send("Missing Recording URL");
    }
    else {
        try {
            // Step 1: Upload the audio file to AssemblyAI
            const uploadResponse = await axios_1.default.post("https://api.assemblyai.com/v2/upload", { url: `${RecordingUrl}.mp3` }, {
                headers: {
                    Authorization: utils_1.assemblyAIApiKey.value(),
                    "content-type": "application/json",
                },
            });
            const audioUrl = uploadResponse.data.upload_url;
            // Step 2: Submit the audio for transcription
            const transcriptionResponse = await axios_1.default.post("https://api.assemblyai.com/v2/transcript", {
                audio_url: audioUrl,
                language_code: "en_us",
                speaker_labels: true, // Enable speaker diarization
            }, {
                headers: {
                    Authorization: utils_1.assemblyAIApiKey.value(),
                    "content-type": "application/json",
                },
            });
            const transcriptId = transcriptionResponse.data.id;
            logger.info(`Transcription ID: ${transcriptId}`);
            // Poll AssemblyAI for transcription completion
            const checkTranscription = async () => {
                try {
                    const result = await axios_1.default.get(`https://api.assemblyai.com/v2/transcript/${transcriptId}`, {
                        headers: {
                            Authorization: utils_1.assemblyAIApiKey.value(),
                        },
                    });
                    if (result.data.status === "completed") {
                        logger.info(`Transcription Completed: ${result.data.text}`);
                        await handelLeadDocumentVoice(result.data, req.body);
                        res.writeHead(200, { "Content-Type": "text/xml" });
                        res.end("Sent message successfully.");
                    }
                    else if (result.data.status === "error") {
                        logger.error("Transcription failed:", result.data.error);
                        throw new Error(`Transcription failed:, ${result.data.error}`);
                    }
                    else {
                        setTimeout(checkTranscription, 5000); // Retry after 5 seconds
                    }
                }
                catch (error) {
                    res.writeHead(200, { "Content-Type": "text/xml" });
                    res.end("Sent message successfully.");
                    logger.error("Error checking transcription status:", error);
                }
            };
            await checkTranscription();
            res.writeHead(200, { "Content-Type": "text/xml" });
            res.end("Sent message successfully.");
        }
        catch (error) {
            logger.error("Error during transcription process:", error);
            res.status(500).send("Error processing transcription");
        }
    }
    res.sendStatus(200);
});
// Facebook Messenger Webhook Verification
app.get("/facebook-webhook", (req, res) => {
    const mode = req.query["hub.mode"];
    const token = req.query["hub.verify_token"];
    const challenge = req.query["hub.challenge"];
    if (mode && token === utils_1.facebookVerifyToken.value()) {
        res.status(200).send(challenge);
    }
    else {
        res.sendStatus(403);
    }
});
// Handle incoming messages
app.post("/facebook-webhook", async (req, res) => {
    const body = req.body;
    if (body.object === "page") {
        for (const entry of body.entry) {
            for (const event of entry.messaging) {
                if (event.message && event.message.text) {
                    if ([
                        "I'm here to assist you with Liftt's premium garage doors and related services. How can I help you today?",
                        "How can I assist you with Liftt's premium garage doors and related services today?",
                        "Liftt offers a variety of garage door sizes to suit different needs:",
                    ].includes(event.message.text)) {
                        res.writeHead(200, { "Content-Type": "text/xml" });
                        res.end("Sent message successfully.");
                        return;
                    }
                    const senderId = event.sender.id;
                    await sendMessengerSenderAction(senderId, "mark_seen");
                    // Show the typing effect
                    await sendMessengerSenderAction(senderId, "typing_on");
                    // Generate response using OpenAI
                    try {
                        const messageBody = await (0, chatbots_1.handleUserQuery)("facebook", event);
                        // Send response back to Facebook Messenger
                        await (0, chatbots_1.sendMessengerMessage)(senderId, messageBody);
                        await sendMessengerSenderAction(senderId, "typing_off");
                        res.writeHead(200, { "Content-Type": "text/xml" });
                        res.end("Sent message successfully.");
                    }
                    catch (error) {
                        logger.error("Error in Facebook webhook:", error);
                        // const twiml = new MessagingResponse();
                        // twiml.message("Sorry, there was an error processing your request.");
                        await (0, chatbots_1.sendMessengerMessage)(senderId, "Sorry, there was an error processing your request.");
                        await sendMessengerSenderAction(senderId, "typing_off");
                        res.writeHead(200, { "Content-Type": "text/xml" });
                        res.end("Sorry, there was an error processing your request.");
                    }
                }
            }
        }
    }
    else {
        res.writeHead(200, { "Content-Type": "text/xml" });
        res.end("Sorry, there was an error processing your request.");
    }
});
// Handle incoming messages
app.post("/telegram-webhook", async (req, res) => {
    const body = req.body;
    const chatId = body.message.chat.id;
    try {
        await (0, chatbots_1.sendTelegramIncomingMessage)(chatId);
        const messageBody = await (0, chatbots_1.handleUserQuery)("telegram", body);
        // Send response back to Facebook Messenger
        await (0, chatbots_1.sendTelegramMessage)(chatId, messageBody);
        res.writeHead(200, { "Content-Type": "text/xml" });
        res.end("Sent message successfully.");
    }
    catch (error) {
        logger.error("Error in Telegram webhook:", error);
        // const twiml = new MessagingResponse();
        // twiml.message("Sorry, there was an error processing your request.");
        await (0, chatbots_1.sendTelegramMessage)(chatId, "Sorry, there was an error processing your request.");
        res.writeHead(200, { "Content-Type": "text/xml" });
        res.end("Sorry, there was an error processing your request.");
    }
    res.writeHead(200, { "Content-Type": "text/xml" });
    res.end("EVENT_RECEIVED");
});
// Endpoint to handle incoming emails
app.post("/email-webhook-listener", async (req, res) => {
    try {
        // Log the entire request body for debugging
        // req is express style request object
        const parts = multipart.parse(req.body, "xYzZY");
        const parsed = {};
        for (const part of parts) {
            const { name, data } = part;
            parsed[name] = data.toString();
        }
        if (!req.body || Object.keys(req.body).length === 0) {
            throw new Error("Bad request: Empty body received");
        }
        // Extract relevant email data from the SendGrid Inbound Parse webhook payload
        let { to, from, subject, text, html, attachments } = parsed;
        // Log the extracted email data
        logger.info("Extracted email data:", {
            to,
            from,
            subject,
            text,
            html,
            attachments,
        });
        // Check if this is a contact form email from Liftt website
        const isContactForm = text &&
            text.includes("This e-mail was sent from a contact form on Liftt");
        if (isContactForm && from.includes("<EMAIL>")) {
            // Extract the actual sender's email from the body
            const emailRegex = /From: .+<([^>]+)>|([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
            const emailMatch = text.match(emailRegex);
            if (emailMatch) {
                // Replace the from address with the actual sender's email
                const actualEmail = emailMatch[1] || emailMatch[2];
                logger.info("Contact form submission - original from:", from);
                logger.info("Contact form submission - actual sender:", actualEmail);
                from = actualEmail;
                parsed.from = actualEmail;
            }
        }
        // Filter spam/no-reply emails
        const spamPatterns = [
            // No-reply variations
            "noreply",
            "no-reply",
            "donotreply",
            "do-not-reply",
            "no.reply",
            // System emails
            "automated",
            "notification",
            "alert",
            "system",
            "auto-confirm",
            "mailer-daemon",
            "postmaster",
            "bounces",
            "bounce",
            "daemon",
            // Common department emails
            "marketing@",
            "newsletter@",
            "info@",
            "support@",
            "contact@",
            "admin@",
            "help@",
            "service@",
            "updates@",
            "news@",
            "hello@",
            "support@",
            "invoices@",
            "invoice@",
            "billing@",
            "accounts@",
            "customerservice@",
            "feedback@",
            "enquiry@",
            "inquiry@",
            // Promotional
            "promotions@",
            "offers@",
            "deals@",
            "sales@",
            "discount@",
            "campaign@",
            "special@",
            "events@",
            "invitation@",
            "invite@",
            // Notifications
            "notifications@",
            "alerts@",
            "updates@",
            "reminder@",
            "notice@",
            "announcement@",
            "broadcast@",
            "digest@",
            // Security
            "security@",
            "verification@",
            "confirm@",
            "authenticate@",
            "validate@",
            "password@",
            "recovery@",
            "reset@",
            "2fa@",
            // Social media
            "facebook@",
            "twitter@",
            "instagram@",
            "linkedin@",
            "pinterest@",
            "youtube@",
            "social@",
            "connect@",
            // Domains
            "@mailchimp.com",
            "@sendgrid.net",
            "@mailgun.org",
            "@constant.com",
            "@amazonses.com",
            "@email-server.com",
            "@salesforce.com",
            "@hubspot.com",
            "@marketo.com",
            "@campaign-monitor.com",
            "@klaviyo.com",
            "@mailerlite.com",
            "@aweber.com",
            "@getresponse.com",
            "@activecampaign.com",
        ];
        const isSpamEmail = spamPatterns.some((pattern) => from.toLowerCase().includes(pattern));
        if (isSpamEmail) {
            logger.info("Irrelevant email detected:", from);
            res.status(200).send("Email received but ignored as irrelevant.");
            return;
        }
        if (to === from ||
            from.includes("<EMAIL>") ||
            from.includes("<EMAIL>")) {
            logger.info("Bad request: Email sent to itself or from sales email");
            res.status(200).send("Email received and processed successfully.");
            return;
        }
        const messageBody = await (0, chatbots_1.handleUserQuery)("email", parsed);
        // Use SendGrid to send the response email
        const msg = {
            to: from, // Recipient's email address
            from: to, // Dynamically set the sender email
            subject: subject, // Email subject
            html: (0, utils_1.getEmailTemplate)(messageBody.replace(/```html\s*|\s*```/g, "")), // Plain text body
        };
        mail_1.default.setApiKey(utils_1.sendGridApiKey.value());
        await mail_1.default.send(msg);
        // Respond to SendGrid with a 200 OK status
        res.status(200).send("Email received and processed successfully.");
    }
    catch (error) {
        logger.error("Error processing email:", error);
        // Respond with a 500 error if something goes wrong
        res.status(200).send("Email received and processed successfully.");
    }
});
app.get("/process-call/:callSid?", async (req, res) => {
    const { callSid } = req.params;
    const { date_from, date_to } = req.query;
    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!date_from || !date_to) {
        res.status(400).send("Both date_from and date_to are required.");
        return;
    }
    if (!dateRegex.test(date_from) ||
        !dateRegex.test(date_to)) {
        res.status(400).send("Invalid date format. Use YYYY-MM-DD.");
        return;
    }
    try {
        // Step 1: Fetch call details
        await (0, calls_1.getCallDetails)(callSid !== null && callSid !== void 0 ? callSid : null, {
            date_from: date_from,
            date_to: date_to,
        });
        res.status(200).send("Call SID processed successfully.");
    }
    catch (error) {
        logger.error("Error processing call:", error);
        res.status(500).send("Internal Server Error");
    }
    return;
});
app.post("/process-call", async (req, res) => {
    const { callSid } = req.body;
    logger.info("Studio flow called:", req.body);
    logger.info("Call SID to process:", callSid);
    if (!callSid) {
        res.status(400).send("No call SID provided.");
    }
    else {
        try {
            // Step 1: Fetch call details
            await (0, calls_1.getCallDetails)(callSid);
            res.status(200).send("Call SID processed successfully.");
        }
        catch (error) {
            logger.error("Error processing call:", error);
            res.status(500).send("Internal Server Error");
        }
    }
});
app.post("/process-form", async (req, res) => {
    try {
        await (0, forms_1.handelLeadDocumentForm)(req.body);
        res.status(200).send("Form processed successfully.");
    }
    catch (error) {
        logger.error("Error processing form:", error);
        res.status(500).send("Internal Server Error");
    }
});
app.post("/vapi-callback", async (req, res) => {
    try {
        const reqBody = req.body.message;
        await (0, vapi_1.handelLeadDocumentVapiCall)(reqBody);
        res.status(200).send("Vapi Callback processed successfully.");
    }
    catch (error) {
        logger.error("Error processing Vapi Callback:", error);
        res.status(500).send("Internal Server Error");
    }
});
app.post("/listen-to-appointments", async (req, res) => {
    var _a;
    try {
        const channelToken = req.headers["x-goog-channel-token"];
        if (channelToken !== utils_1.tokenKey.replace(/\n/g, "")) {
            logger.error("Invalid token");
            return res.status(401).send("Invalid token");
        }
        const auth = await (0, utils_1.getDriveClient)();
        // Initialize variables for pagination
        let nextPageToken = null;
        let allEvents = [];
        let batch = admin.firestore().batch();
        // Set up time range for events
        const timeMin = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
        const timeMax = new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString();
        const datesProcessed = [];
        // Fetch events with pagination
        do {
            const eventResponse = await utils_1.calendar.events.list({
                auth: auth,
                calendarId: utils_1.lifttCalendarId,
                timeMax: timeMax,
                timeMin: timeMin,
                maxResults: 50, // Smaller page size for better performance
                singleEvents: true,
                showDeleted: true,
                orderBy: "updated",
                pageToken: nextPageToken || undefined,
            });
            // Store the next page token
            nextPageToken = eventResponse.data.nextPageToken || null;
            // Process current page of events
            if (((_a = eventResponse.data) === null || _a === void 0 ? void 0 : _a.items) && eventResponse.data.items.length > 0) {
                const currentPageCount = eventResponse.data.items.length;
                allEvents = [...allEvents, ...eventResponse.data.items];
                logger.info(`Processing page with ${currentPageCount} calendar events (total so far: ${allEvents.length})`);
                // Process events in current page
                for (const item of eventResponse.data.items) {
                    try {
                        await (0, appointments_1.processLeadAppointmentFromCalendarEvent)(batch, item);
                        // store all events that has appointments
                        if (item.summary &&
                            !item.summary.includes("No Appointments") &&
                            item.status !== "cancelled") {
                            datesProcessed.push(item.start.dateTime);
                        }
                        logger.info(`Completed processing event: ${item.id}`);
                    }
                    catch (error) {
                        logger.error(`Error processing calendar event ${item.id}:`, error);
                    }
                }
            }
            else {
                logger.info("No calendar events found in current page");
            }
            await batch.commit();
            // Create a new batch for the next set of operations
            batch = admin.firestore().batch();
            // Continue fetching pages until there are no more
        } while (nextPageToken);
        // get all the dates that does not have appointments using timeMin and timeMax
        const datesWithoutAppointments = getDatesWithoutAppointments(timeMin, timeMax, datesProcessed);
        if ((datesWithoutAppointments === null || datesWithoutAppointments === void 0 ? void 0 : datesWithoutAppointments.length) > 0) {
            try {
                // use openai to get the available slots for the datesWithoutAppointments make the array of objects with human readable time slots to be used in calls
                const openai = (0, utils_1.invokeOpenAI)();
                const response = await openai.chat.completions.create({
                    model: "gpt-4o",
                    messages: [
                        {
                            role: "system",
                            content: "You are given a list of available appointment dates. Remove all weekend dates (Saturday and Sunday). Detect full weeks (all 5 weekdays). If multiple full weeks are consecutive, group them as 'Available the entire week of [Month] [Day] until [Month] [Day]'. For partial weeks, use 'Monday, June 17 until Wednesday, June 19'. Combine all ranges into one line separated by commas. End with '(9am to 12pm)' once. Do not repeat the time for each range. Respond only with the formatted plain text output.",
                        },
                        {
                            role: "user",
                            content: `Available dates:\n${JSON.stringify(datesWithoutAppointments)}\n\nExample output:\nAvailable the entire week of June 17 until June 28, Monday, July 1 until Thursday, July 11 (9am to 12pm)`,
                        },
                    ],
                });
                const result = response.choices[0].message.content;
                // update the datesWithoutAppointments in the ai_params collection
                const aiFollowUpDoc = admin
                    .firestore()
                    .collection("settings")
                    .doc("ai_params");
                batch.set(aiFollowUpDoc, {
                    followup: { datesWithoutAppointments: result },
                }, { merge: true });
            }
            catch (error) {
                logger.error(`Error processing dates without appointments:`, error);
            }
        }
        // Commit any remaining operations in the batch
        await batch.commit();
        logger.info(`All calendar events processed successfully `);
        return res.status(200).send("Success");
    }
    catch (error) {
        logger.error("Error processing event:", error instanceof Error ? error.message : "Unknown error");
        return res.status(500).send("Internal Server Error");
    }
});
const getDatesWithoutAppointments = (timeMin, timeMax, datesProcessed) => {
    const startDate = new Date(timeMin);
    const endDate = new Date(timeMax);
    const datesWithoutAppointments = [];
    const currentDate = new Date(startDate);
    // Convert appointment dates to YYYY-MM-DD format for comparison
    const appointmentDates = datesProcessed.map((dateString) => {
        // Parse the ISO date with timezone offset
        const date = new Date(dateString);
        // Format to YYYY-MM-DD
        return date.toISOString().split("T")[0];
    });
    logger.info(`Normalized appointment dates: ${JSON.stringify(appointmentDates)}`);
    while (currentDate <= endDate) {
        // Skip weekends (0 = Sunday, 6 = Saturday)
        const dayOfWeek = currentDate.getDay();
        if (dayOfWeek !== 0 && dayOfWeek !== 6) {
            const dateString = currentDate.toISOString().split("T")[0];
            // Check if this date has any appointments
            const hasAppointment = appointmentDates.includes(dateString);
            if (!hasAppointment) {
                datesWithoutAppointments.push(dateString);
            }
        }
        // Move to next day
        currentDate.setDate(currentDate.getDate() + 1);
    }
    logger.info(`Found ${datesWithoutAppointments.length} weekdays without appointments`);
    return datesWithoutAppointments;
};
app.get("/auth-facebook-callback", async (req, res) => {
    try {
        const code = req.query.code;
        if (!code) {
            logger.error("No authorization code provided");
            return res.status(400).send("Authorization code missing");
        }
        logger.info("Received authorization code:", code);
        // Exchange code for access token (using authorization code flow)
        const tokenResponse = await axios_1.default.get("https://graph.facebook.com/v18.0/oauth/access_token", {
            params: {
                client_id: utils_1.facebookAppId.value(),
                client_secret: utils_1.facebookAppSecret.value(),
                redirect_uri: "https://us-central1-wyspre-ai.cloudfunctions.net/chatBots/auth-facebook-callback",
                code: code
            },
        });
        const { access_token } = tokenResponse.data;
        // Get user email and other profile info
        const userResponse = await axios_1.default.get("https://graph.facebook.com/v18.0/me", {
            params: {
                fields: "id,email,name",
                access_token,
            },
        });
        logger.info("User info retrieved:", userResponse.data);
        // Store user info in Firestore if needed
        if (userResponse.data.id && userResponse.data.email) {
            const leadsCollection = admin.firestore().collection("leads");
            const leadsSnapshot = await leadsCollection
                .where("source_id", "==", userResponse.data.id)
                .where("lead_source", "==", "facebook")
                .get();
            if (!leadsSnapshot.empty) {
                const leadDoc = leadsSnapshot.docs[0];
                await leadDoc.ref.update({
                    email: userResponse.data.email,
                    updated_at: admin.firestore.FieldValue.serverTimestamp()
                });
                logger.info(`Updated lead ${leadDoc.id} with email: ${userResponse.data.email}`);
            }
        }
        // Return a success page to the user
        res.send(`
      <html>
        <head><title>Authentication Successful</title></head>
        <body>
          <h1>Authentication Successful</h1>
          <p>You can now close this window and return to the conversation.</p>
          <script>setTimeout(function() { window.close(); }, 3000);</script>
        </body>
      </html>
    `);
    }
    catch (error) {
        logger.error("OAuth error:", error);
        res.status(500).send("Authentication failed. Please try again.");
    }
});
// Export the chatbots webhook handler
exports.chatBots = (0, https_1.onRequest)({
    cors: true,
    secrets: [
        utils_1.openAIKey,
        utils_1.twilioAuthTpken,
        utils_1.twilioAccounSID,
        utils_1.twilioWhatsappNumber,
        utils_1.facebookVerifyToken,
        utils_1.facebookPageAccessToken,
        utils_1.telegramBotToken,
        utils_1.sendGridApiKey,
        utils_1.assemblyAIApiKey,
        utils_1.googleServiceAccountEmail,
        utils_1.googleServiceAccountKey,
        utils_1.facebookAppId,
    ],
    cpu: 8,
    memory: "16GiB",
    timeoutSeconds: 3600,
}, app);
//# sourceMappingURL=whatsappbot.js.map