"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handelLeadDocumentFacebook = handelLeadDocumentFacebook;
exports.sendMessengerMessage = sendMessengerMessage;
const axios_1 = __importDefault(require("axios"));
const utils_1 = require("../utils");
const logger = __importStar(require("firebase-functions/logger"));
const admin = __importStar(require("firebase-admin"));
async function handelLeadDocumentFacebook(req) {
    var _a;
    const { sender } = req;
    const psid = sender.id;
    const leadsCollection = admin.firestore().collection("leads");
    const currentTime = Math.floor(Date.now() / 1000);
    // Search for an existing lead by WaId (WhatsApp ID)
    const leadsSnapshot = await leadsCollection
        .where("source_id", "==", psid)
        .where("lead_source", "==", "facebook")
        .where("lead_actions", "==", "inquired")
        .get();
    let leadDocRef, leadData;
    if (!leadsSnapshot.empty) {
        // Lead exists, get the reference and data
        const leadDoc = leadsSnapshot.docs[0];
        leadDocRef = leadDoc.ref;
        leadData = leadDoc.data();
    }
    else {
        let record;
        try {
            record = await axios_1.default.get(`https://graph.facebook.com/${psid}?fields=email,name,locale,timezone,first_name,last_name,profile_pic&access_token=${utils_1.facebookPageAccessToken.value()}`);
            logger.info("Webhook facebook invoked:", record.data);
            // Log the full response for debugging
            logger.info(`Facebook API response status: ${record.status}`);
            if (record.status !== 200) {
                logger.warn(`Facebook API error: ${JSON.stringify(record.data)}`);
            }
            // Log if email was returned
            if ((_a = record.data) === null || _a === void 0 ? void 0 : _a.email) {
                logger.info("Successfully retrieved email for user:", psid);
            }
            else {
                logger.info("Email not available for user:", psid);
            }
        }
        catch (error) {
            logger.error("Failed to fetch Facebook profile data:", error);
            record = {
                data: {
                    email: "",
                    name: "",
                    first_name: "",
                    last_name: "",
                    profile_pic: "",
                    gender: "",
                    locale: "",
                    timezone: "",
                },
            }; // Continue with empty data if there's an error
        }
        const { name, first_name, last_name, profile_pic, gender, locale, timezone, email, } = record.data;
        // Create a new lead document with an autogenerated ID
        leadDocRef = leadsCollection.doc(); // Auto-generate document ID
        leadData = {
            id: leadDocRef.id,
            source_id: psid,
            email: email !== null && email !== void 0 ? email : "",
            name: name !== null && name !== void 0 ? name : "",
            locale: locale !== null && locale !== void 0 ? locale : "",
            timezone: timezone !== null && timezone !== void 0 ? timezone : "",
            first_name: first_name !== null && first_name !== void 0 ? first_name : "",
            last_name: last_name !== null && last_name !== void 0 ? last_name : "",
            profile_pic: profile_pic !== null && profile_pic !== void 0 ? profile_pic : "",
            gender: gender !== null && gender !== void 0 ? gender : "",
            lead_source: "facebook", // Track the source of the lead
            lastMessageTime: currentTime,
            lead_details: {
                psid: psid,
                name: name !== null && name !== void 0 ? name : "",
                locale: locale !== null && locale !== void 0 ? locale : "",
                timezone: timezone !== null && timezone !== void 0 ? timezone : "",
                first_name: first_name !== null && first_name !== void 0 ? first_name : "",
                last_name: last_name !== null && last_name !== void 0 ? last_name : "",
                profile_pic: profile_pic !== null && profile_pic !== void 0 ? profile_pic : "",
                gender: gender !== null && gender !== void 0 ? gender : "",
            },
            conversations: [],
        };
    }
    // if no email then send request to access email
    if (!leadData.email) {
        await requestEmailPermission(psid);
    }
    return { leadDocRef: leadDocRef, leadData: leadData };
}
// Function to send a message via Facebook Messenger
async function sendMessengerMessage(recipientId, messageText) {
    try {
        await axios_1.default.post(`https://graph.facebook.com/me/messages?access_token=${utils_1.facebookPageAccessToken.value()}`, {
            recipient: { id: recipientId },
            message: { text: messageText },
        }, {
            headers: { "Content-Type": "application/json" },
        });
    }
    catch (error) {
        logger.error("Error sending message to Facebook Messenger:", error);
    }
}
// Function to request app access for email permission
async function requestEmailPermission(recipientId) {
    try {
        // Create the login URL with proper permissions
        const loginUrl = `https://www.facebook.com/v18.0/dialog/oauth?client_id=${utils_1.facebookAppId.value()}&redirect_uri=${encodeURIComponent("https://us-central1-wyspre-ai.cloudfunctions.net/chatBots/auth-facebook-callback")}&state=${recipientId}&scope=email`;
        await axios_1.default.post(`https://graph.facebook.com/v18.0/me/messages?access_token=${utils_1.facebookPageAccessToken.value()}`, {
            recipient: { id: recipientId },
            message: {
                attachment: {
                    type: "template",
                    payload: {
                        template_type: "button",
                        text: "To provide better service, please share your email address with us.",
                        buttons: [
                            {
                                type: "web_url",
                                url: loginUrl,
                                title: "Share Email",
                                webview_height_ratio: "compact"
                            }
                        ],
                    },
                },
            },
        }, {
            headers: { "Content-Type": "application/json" },
        });
        logger.info("Email permission request sent to user:", recipientId);
    }
    catch (error) {
        logger.error("Error requesting email permission:", error);
    }
}
//# sourceMappingURL=facebook.js.map