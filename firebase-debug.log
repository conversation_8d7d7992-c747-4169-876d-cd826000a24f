[debug] [2025-06-25T08:34:24.266Z] ----------------------------------------------------------------------
[debug] [2025-06-25T08:34:24.271Z] Command:       C:\nvm4w\nodejs\node.exe C:\nvm4w\nodejs\node_modules\firebase-tools\lib\bin\firebase.js deploy --only functions:chatBots
[debug] [2025-06-25T08:34:24.272Z] CLI Version:   14.8.0
[debug] [2025-06-25T08:34:24.272Z] Platform:      win32
[debug] [2025-06-25T08:34:24.272Z] Node Version:  v22.10.0
[debug] [2025-06-25T08:34:24.274Z] Time:          Wed Jun 25 2025 16:34:24 GMT+0800 (Philippine Standard Time)
[debug] [2025-06-25T08:34:24.274Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-25T08:34:24.698Z] Object "/extensions" in "firebase.json" has unknown property: {"additionalProperty":"firestore-algolia-search"}
[debug] [2025-06-25T08:34:24.710Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-25T08:34:24.710Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-25T08:34:24.710Z] [iam] checking project wyspre-ai for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-06-25T08:34:24.712Z] No OAuth tokens found
[debug] [2025-06-25T08:34:24.712Z] No OAuth tokens found
[debug] [2025-06-25T08:34:24.713Z] > refreshing access token with scopes: []
[debug] [2025-06-25T08:34:24.718Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-25T08:34:24.718Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-25T08:34:24.953Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-25T08:34:24.954Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-25T08:34:24.960Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/wyspre-ai:testIamPermissions [none]
[debug] [2025-06-25T08:34:24.960Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/wyspre-ai:testIamPermissions x-goog-quota-user=projects/wyspre-ai
[debug] [2025-06-25T08:34:24.961Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wyspre-ai:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-06-25T08:34:25.884Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/wyspre-ai:testIamPermissions 200
[debug] [2025-06-25T08:34:25.885Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wyspre-ai:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-06-25T08:34:25.885Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-25T08:34:25.885Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-25T08:34:25.885Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/wyspre-ai/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-06-25T08:34:25.885Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/wyspre-ai/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-06-25T08:34:27.038Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/wyspre-ai/serviceAccounts/<EMAIL>:testIamPermissions 200
[debug] [2025-06-25T08:34:27.039Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/wyspre-ai/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[info] 
[info] === Deploying to 'wyspre-ai'...
[info] 
[info] i  deploying functions 
[info] Running command: npm --prefix "$RESOURCE_DIR" run lint
